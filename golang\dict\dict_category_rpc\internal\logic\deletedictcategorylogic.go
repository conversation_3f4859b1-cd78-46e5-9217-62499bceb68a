package logic

import (
	"context"
	"errors"
	"strconv"

	"dict_category_rpc/dict_category"
	"dict_category_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type DeleteDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictCategoryLogic {
	return &DeleteDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除字典分类
func (l *DeleteDictCategoryLogic) DeleteDictCategory(in *dict_category.DeleteDictCategoryReq) (*dict_category.DeleteDictCategoryResp, error) {
	// 参数验证
	if in.Id <= 0 {
		return &dict_category.DeleteDictCategoryResp{
			Success: false,
			Message: "分类ID不能为空",
		}, nil
	}

	// 检查字典分类是否存在
	_, err := l.svcCtx.DictCategoryModel.FindByID(in.Id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &dict_category.DeleteDictCategoryResp{
				Success: false,
				Message: "字典分类不存在",
			}, nil
		}
		return &dict_category.DeleteDictCategoryResp{
			Success: false,
			Message: "查询字典分类失败: " + err.Error(),
		}, nil
	}

	// 检查该分类下是否存在字典项
	hasItems, err := l.svcCtx.DictCategoryModel.CheckDictItemsExist(in.Id)
	if err != nil {
		return &dict_category.DeleteDictCategoryResp{
			Success: false,
			Message: "检查字典项失败: " + err.Error(),
		}, nil
	}

	if hasItems {
		// 获取字典项数量用于提示
		count, _ := l.svcCtx.DictCategoryModel.GetDictItemsCountByCategory(in.Id)
		return &dict_category.DeleteDictCategoryResp{
			Success: false,
			Message: "该分类下还有 " + strconv.FormatInt(count, 10) + " 个字典项，请先删除相关字典项",
		}, nil
	}

	// 删除字典分类
	err = l.svcCtx.DictCategoryModel.Delete(in.Id)
	if err != nil {
		return &dict_category.DeleteDictCategoryResp{
			Success: false,
			Message: "删除字典分类失败: " + err.Error(),
		}, nil
	}

	return &dict_category.DeleteDictCategoryResp{
		Success: true,
		Message: "删除字典分类成功",
	}, nil
}
